<template>
  <div class="dashboard">
    <!-- 顶部导航 -->
    <nav class="dashboard-nav">
      <div class="nav-content">
        <div class="nav-left">
          <h1 class="nav-title">
            <i class="fas fa-music"></i>
            乐队管理系统
          </h1>
        </div>
        <div class="nav-right">
          <div class="user-info">
            <span class="welcome-text">欢迎，{{ authStore.displayName }}</span>
            <div class="user-menu">
              <button class="user-avatar" @click="showUserMenu = !showUserMenu">
                <i class="fas fa-user-circle"></i>
              </button>
              <div v-if="showUserMenu" class="user-dropdown">
                <router-link to="/profile" class="dropdown-item">
                  <i class="fas fa-user"></i>
                  个人资料
                </router-link>
                <button @click="handleLogout" class="dropdown-item">
                  <i class="fas fa-sign-out-alt"></i>
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="dashboard-content">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <h2>管理员仪表板</h2>
        <p>管理您的乐队、成员和活动信息</p>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon bands">
            <i class="fas fa-music"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.bands }}</h3>
            <p>乐队数量</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon members">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.members }}</h3>
            <p>成员总数</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon events">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.events }}</h3>
            <p>活动数量</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon active">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.activeEvents }}</h3>
            <p>进行中活动</p>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h3>快速操作</h3>
        <div class="action-grid">
          <router-link to="/bands" class="action-card">
            <i class="fas fa-plus-circle"></i>
            <span>添加乐队</span>
          </router-link>
          
          <router-link to="/members" class="action-card">
            <i class="fas fa-user-plus"></i>
            <span>添加成员</span>
          </router-link>
          
          <router-link to="/events" class="action-card">
            <i class="fas fa-calendar-plus"></i>
            <span>创建活动</span>
          </router-link>
          
          <router-link to="/gallery" class="action-card">
            <i class="fas fa-images"></i>
            <span>图片管理</span>
          </router-link>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="recent-section">
        <h3>最近活动</h3>
        <div class="recent-list">
          <div v-if="recentActivities.length === 0" class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>暂无最近活动</p>
          </div>
          <div v-else v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon">
              <i :class="activity.icon"></i>
            </div>
            <div class="activity-content">
              <p class="activity-text">{{ activity.text }}</p>
              <span class="activity-time">{{ activity.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const authStore = useAuthStore()

// 界面状态
const showUserMenu = ref(false)
const loading = ref(false)

// 统计数据
const stats = ref({
  bands: 0,
  members: 0,
  events: 0,
  activeEvents: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    icon: 'fas fa-music',
    text: '创建了新乐队 "摇滚之星"',
    time: '2小时前'
  },
  {
    id: 2,
    icon: 'fas fa-user-plus',
    text: '添加了新成员 "张三"',
    time: '5小时前'
  },
  {
    id: 3,
    icon: 'fas fa-calendar-plus',
    text: '创建了新活动 "音乐节演出"',
    time: '1天前'
  }
])

// 处理登出
const handleLogout = () => {
  authStore.logout()
  router.push('/auth/login')
}

// 加载统计数据
const loadStats = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取统计数据
    // 这里使用模拟数据
    stats.value = {
      bands: 5,
      members: 23,
      events: 8,
      activeEvents: 3
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.user-menu')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  loadStats()
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  background: #f7fafc;
}

.dashboard-nav {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.nav-title i {
  color: #667eea;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-text {
  color: #4a5568;
  font-weight: 500;
}

.user-menu {
  position: relative;
}

.user-avatar {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #667eea;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s;
}

.user-avatar:hover {
  background: rgba(102, 126, 234, 0.1);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #4a5568;
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background 0.2s;
}

.dropdown-item:hover {
  background: #f7fafc;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-section h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: #718096;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon.bands { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.members { background: linear-gradient(135deg, #f093fb, #f5576c); }
.stat-icon.events { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.stat-icon.active { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.25rem 0;
}

.stat-content p {
  color: #718096;
  margin: 0;
  font-size: 0.9rem;
}

.quick-actions, .recent-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-actions h3, .recent-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  text-decoration: none;
  color: #4a5568;
  transition: all 0.2s;
}

.action-card:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.action-card i {
  font-size: 2rem;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #a0aec0;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: background 0.2s;
}

.activity-item:hover {
  background: #f7fafc;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-weight: 500;
}

.activity-time {
  color: #a0aec0;
  font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }
  
  .dashboard-content {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
