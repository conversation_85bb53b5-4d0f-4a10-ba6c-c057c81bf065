/**
 * 统计数据服务
 * 处理仪表板统计数据相关的API调用
 */

import { apiRequest } from './apiService'

export interface DashboardStats {
  bands: number
  members: number
  events: number
  activeEvents: number
}

export interface RecentActivity {
  id: string
  icon: string
  text: string
  time: string
  timestamp: string
}

/**
 * 获取仪表板统计数据
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  const response = await apiRequest<DashboardStats>('/api/stats/dashboard', {
    method: 'GET'
  })
  return response
}

/**
 * 获取最近活动记录
 */
export const getRecentActivities = async (): Promise<RecentActivity[]> => {
  const response = await apiRequest<RecentActivity[]>('/api/stats/recent-activities', {
    method: 'GET'
  })
  return response
}
